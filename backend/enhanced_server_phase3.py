"""
Enhanced FastAPI Backend Server with Phase 3 Advanced Features
Provides API endpoints for the enhanced scraper pipeline with all Phase 3 components
"""

import os
import sys
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging
import json
import asyncio
from datetime import datetime
import time

# Add project root to path for imports
sys.path.append('/app')
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import Phase 3 enhanced pipeline
try:
    from enhanced_scraper_pipeline_phase3 import EnhancedScraperPipelinePhase3
    ENHANCED_PIPELINE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Enhanced pipeline not available: {e}")
    ENHANCED_PIPELINE_AVAILABLE = False

# Fallback to traditional pipeline
from scraper_pipeline import Scraper<PERSON><PERSON>eline

# Initialize FastAPI app
app = FastAPI(title="AI Navigator Enhanced Scraper API", version="2.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Global pipeline instances
traditional_pipeline = None
enhanced_pipeline = None

# Pydantic models
class EnhancedScrapingJobRequest(BaseModel):
    tools: List[Dict[str, str]]  # List of tools with name and url
    use_parallel: Optional[bool] = True
    use_phase3: Optional[bool] = True
    max_items: Optional[int] = 50

class TraditionalScrapingJobRequest(BaseModel):
    spider_name: str
    max_items: Optional[int] = 50

class ScrapingJobResponse(BaseModel):
    success: bool
    job_id: Optional[str] = None
    message: str
    stats: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class EnhancedJobResponse(BaseModel):
    success: bool
    job_id: str
    message: str
    total_tools: int
    processing_mode: str
    phase3_enabled: bool
    estimated_time: Optional[float] = None

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Job tracking
active_jobs = {}

@app.on_event("startup")
async def startup_event():
    """Initialize both traditional and enhanced pipelines on startup"""
    global traditional_pipeline, enhanced_pipeline
    
    try:
        # Initialize traditional pipeline
        traditional_pipeline = ScraperPipeline()
        logger.info("Traditional scraper pipeline initialized successfully")
        
        # Initialize enhanced pipeline if available
        if ENHANCED_PIPELINE_AVAILABLE:
            api_key = '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'  # Perplexity API key
            enhanced_pipeline = EnhancedScraperPipelinePhase3(api_key)
            logger.info("Enhanced Phase 3 pipeline initialized successfully")
        else:
            logger.warning("Enhanced Phase 3 pipeline not available - using traditional pipeline only")
            
    except Exception as e:
        logger.error(f"Failed to initialize pipelines: {str(e)}")
        raise

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "traditional_pipeline": traditional_pipeline is not None,
        "enhanced_pipeline": enhanced_pipeline is not None,
        "phase3_available": ENHANCED_PIPELINE_AVAILABLE
    }

@app.get("/api/capabilities")
async def get_capabilities():
    """Get available pipeline capabilities"""
    return {
        "traditional_scraping": traditional_pipeline is not None,
        "enhanced_scraping": enhanced_pipeline is not None,
        "phase3_features": {
            "structured_data_extraction": ENHANCED_PIPELINE_AVAILABLE,
            "advanced_content_analysis": ENHANCED_PIPELINE_AVAILABLE,
            "performance_analysis": ENHANCED_PIPELINE_AVAILABLE,
            "parallel_processing": ENHANCED_PIPELINE_AVAILABLE,
            "caching_system": ENHANCED_PIPELINE_AVAILABLE,
            "performance_monitoring": ENHANCED_PIPELINE_AVAILABLE
        }
    }

@app.get("/api/spiders")
async def get_available_spiders():
    """Get list of available Scrapy spiders"""
    try:
        spiders = traditional_pipeline.get_available_spiders() if traditional_pipeline else []
        return {"spiders": spiders}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-enhanced-scraping", response_model=EnhancedJobResponse)
async def start_enhanced_scraping_job(request: EnhancedScrapingJobRequest, background_tasks: BackgroundTasks):
    """Start an enhanced scraping job with Phase 3 features"""
    try:
        if not enhanced_pipeline:
            raise HTTPException(
                status_code=503, 
                detail="Enhanced pipeline not available. Phase 3 features require proper configuration."
            )
        
        if not request.tools:
            raise HTTPException(status_code=400, detail="No tools provided")
        
        # Validate tool data
        for tool in request.tools:
            if not tool.get('name') or not tool.get('url'):
                raise HTTPException(status_code=400, detail="Each tool must have 'name' and 'url'")
        
        # Generate job ID
        job_id = f"enhanced_{int(time.time())}"
        
        # Estimate processing time (rough estimate)
        estimated_time = len(request.tools) * (2.0 if request.use_parallel else 5.0)
        
        # Track job
        active_jobs[job_id] = {
            "status": "running",
            "start_time": time.time(),
            "total_tools": len(request.tools),
            "phase3_enabled": request.use_phase3,
            "parallel_enabled": request.use_parallel,
            "results": None
        }
        
        # Start enhanced processing in background
        def run_enhanced_scraper():
            try:
                logger.info(f"Starting enhanced scraping job {job_id} with {len(request.tools)} tools")
                
                result = enhanced_pipeline.process_tools_enhanced(
                    request.tools,
                    use_parallel=request.use_parallel
                )
                
                # Update job status
                active_jobs[job_id]["status"] = "completed"
                active_jobs[job_id]["results"] = result
                active_jobs[job_id]["end_time"] = time.time()
                
                logger.info(f"Enhanced scraping job {job_id} completed successfully")
                
            except Exception as e:
                logger.error(f"Enhanced scraping job {job_id} failed: {str(e)}")
                active_jobs[job_id]["status"] = "failed"
                active_jobs[job_id]["error"] = str(e)
                active_jobs[job_id]["end_time"] = time.time()
        
        background_tasks.add_task(run_enhanced_scraper)
        
        return EnhancedJobResponse(
            success=True,
            job_id=job_id,
            message=f"Started enhanced scraping job for {len(request.tools)} tools",
            total_tools=len(request.tools),
            processing_mode="parallel" if request.use_parallel else "sequential",
            phase3_enabled=request.use_phase3,
            estimated_time=estimated_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting enhanced scraping job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-scraping", response_model=ScrapingJobResponse)
async def start_traditional_scraping_job(request: TraditionalScrapingJobRequest, background_tasks: BackgroundTasks):
    """Start a traditional scraping job (legacy endpoint)"""
    try:
        if not traditional_pipeline:
            raise HTTPException(status_code=500, detail="Traditional pipeline not initialized")
        
        # Validate spider name
        available_spiders = traditional_pipeline.get_available_spiders()
        if request.spider_name not in available_spiders:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid spider name. Available: {available_spiders}"
            )
        
        # Start traditional scraping job in background
        def run_traditional_scraper():
            try:
                result = traditional_pipeline.run_spider(request.spider_name, request.max_items)
                logger.info(f"Traditional scraping job completed: {result}")
            except Exception as e:
                logger.error(f"Traditional scraping job failed: {str(e)}")
        
        background_tasks.add_task(run_traditional_scraper)
        
        return ScrapingJobResponse(
            success=True,
            job_id=f"{request.spider_name}_{int(time.time())}",
            message=f"Started traditional scraping job for {request.spider_name}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting traditional scraping job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/job-status/{job_id}")
async def get_job_status(job_id: str):
    """Get status of an enhanced scraping job"""
    if job_id not in active_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = active_jobs[job_id]
    
    # Calculate progress
    progress = 0
    if job["status"] == "completed":
        progress = 100
    elif job["status"] == "running":
        elapsed_time = time.time() - job["start_time"]
        estimated_total = job.get("estimated_time", 60)
        progress = min(90, (elapsed_time / estimated_total) * 100)
    
    response = {
        "job_id": job_id,
        "status": job["status"],
        "progress": progress,
        "total_tools": job["total_tools"],
        "phase3_enabled": job["phase3_enabled"],
        "parallel_enabled": job["parallel_enabled"]
    }
    
    if job["status"] == "completed" and job.get("results"):
        response["results"] = job["results"]
    elif job["status"] == "failed":
        response["error"] = job.get("error", "Unknown error")
    
    return response

@app.get("/api/enhanced-results/{job_id}")
async def get_enhanced_results(job_id: str):
    """Get detailed results from an enhanced scraping job"""
    if job_id not in active_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = active_jobs[job_id]
    
    if job["status"] != "completed":
        raise HTTPException(status_code=400, detail=f"Job status: {job['status']}")
    
    if not job.get("results"):
        raise HTTPException(status_code=404, detail="No results available")
    
    return job["results"]

@app.get("/api/performance-dashboard")
async def get_performance_dashboard():
    """Get performance dashboard data from enhanced pipeline"""
    try:
        if not enhanced_pipeline:
            raise HTTPException(status_code=503, detail="Enhanced pipeline not available")
        
        stats = enhanced_pipeline.get_comprehensive_stats()
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/test-services")
async def test_services():
    """Test all pipeline services"""
    try:
        results = {}
        
        # Test traditional pipeline
        if traditional_pipeline:
            results["traditional_pipeline"] = traditional_pipeline.test_services()
        
        # Test enhanced pipeline components
        if enhanced_pipeline:
            results["enhanced_pipeline"] = {
                "status": "available",
                "components": {
                    "structured_data_extractor": "available",
                    "content_analyzer": "available", 
                    "performance_analyzer": "available",
                    "parallel_processor": "available",
                    "performance_monitor": "available"
                }
            }
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/logs")
async def get_logs(lines: int = 100):
    """Get recent log entries"""
    try:
        log_file = "/app/scraper_pipeline.log"
        logs = []
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                log_lines = f.readlines()
                logs = log_lines[-lines:] if len(log_lines) > lines else log_lines
        
        return {"logs": [line.strip() for line in logs]}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/jobs/{job_id}")
async def cancel_job(job_id: str):
    """Cancel or remove a job"""
    if job_id in active_jobs:
        del active_jobs[job_id]
        return {"message": f"Job {job_id} removed"}
    else:
        raise HTTPException(status_code=404, detail="Job not found")

@app.get("/api/jobs")
async def list_jobs():
    """List all jobs"""
    return {"jobs": active_jobs}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
