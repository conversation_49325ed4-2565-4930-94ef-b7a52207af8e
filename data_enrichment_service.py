"""
Data Enrichment Service using Perplexity API
Enhances basic scraped data with additional information about AI tools.
"""

import requests
import logging
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import json
from config import config
from technical_level_classifier import TechnicalLevelClassifier
from logo_extraction_service import LogoExtractionService
from url_discovery_service import URLDiscoveryService

class DataEnrichmentService:
    def __init__(self, perplexity_api_key: str):
        self.api_key = perplexity_api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.logger = logging.getLogger(__name__)

        # Initialize technical level classifier
        self.technical_classifier = TechnicalLevelClassifier(perplexity_api_key)

        # Initialize logo extraction service
        self.logo_extractor = LogoExtractionService(perplexity_api_key)

        # Initialize URL discovery service
        self.url_discoverer = URLDiscoveryService(perplexity_api_key)
        
    def _call_perplexity(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """Make a call to Perplexity API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "llama-3.1-sonar-small-128k-online",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that provides accurate, factual information about AI tools and software. Always provide specific, actionable details when available."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": 0.2,
                "stream": False
            }
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error calling Perplexity API: {str(e)}")
            return None
    
    def enrich_tool_data(self, tool_name: str, website_url: str, basic_description: str = "") -> Dict[str, Any]:
        """Enrich basic tool information with comprehensive detailed data"""
        
        # Create a more comprehensive prompt for maximum data extraction
        enhanced_prompt = f"""
        Provide comprehensive information about "{tool_name}" (website: {website_url}) in JSON format.
        Research this AI tool thoroughly and provide maximum detail for building the world's best AI tool directory.
        
        {{
            "short_description": "Compelling 1-2 sentence value proposition",
            "description": "Detailed 4-6 sentence description covering functionality, benefits, and competitive advantages",
            "key_features": ["specific feature 1", "specific feature 2", "specific feature 3", "specific feature 4", "specific feature 5"],
            "use_cases": ["specific use case 1", "specific use case 2", "specific use case 3", "specific use case 4"],
            "target_audience": ["primary audience 1", "primary audience 2", "secondary audience 1"],
            "categories": ["primary category", "secondary category"],
            "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
            
            "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES|OPEN_SOURCE",
            "price_range": "FREE|LOW|MEDIUM|HIGH|ENTERPRISE",
            "pricing_details": "Detailed pricing information",
            "has_free_tier": true/false,
            "trial_available": true/false,
            "demo_available": true/false,
            
            "integrations": ["integration1", "integration2", "integration3"],
            "programming_languages": ["language1", "language2"] if applicable,
            "frameworks": ["framework1", "framework2"] if applicable,
            "libraries": ["library1", "library2"] if applicable,
            "deployment_options": ["cloud", "on-premise", "hybrid"] as applicable,
            "supported_os": ["windows", "mac", "linux", "web"] as applicable,
            
            "api_access": true/false,
            "mobile_support": true/false,
            "open_source": true/false,
            "has_live_chat": true/false,
            "learning_curve": "LOW|MEDIUM|HIGH",
            "customization_level": "Low|Medium|High",
            "technical_level": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
            "logo_url": "direct URL to company/tool logo image",
            "documentation_url": "URL to documentation/help pages",
            "contact_url": "URL to contact/support page",
            "privacy_policy_url": "URL to privacy policy",
            "pricing_url": "URL to pricing information",

            "support_channels": ["email", "chat", "phone", "community", "documentation"],
            
            "review_sentiment_label": "Positive|Mixed|Negative|Unknown",
            "review_sentiment_score": 0.0-1.0 or null,
            "review_count": number or null
        }}
        
        IMPORTANT:
        1. Research thoroughly using all available sources
        2. Provide only verified, factual information  
        3. Use "unknown" or null for unverifiable data
        4. Be comprehensive but accurate
        5. Return valid JSON format only
        """
        
        prompt = enhanced_prompt
        
        response = self._call_perplexity(prompt, max_tokens=2000)
        
        if response:
            try:
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    enriched_data = json.loads(json_match.group())

                    # Add technical level classification
                    technical_level = self.technical_classifier.classify_technical_level(enriched_data)
                    enriched_data['technical_level'] = technical_level

                    # Add classification confidence
                    confidence = self.technical_classifier.get_classification_confidence(enriched_data, technical_level)
                    enriched_data['technical_level_confidence'] = confidence

                    # Extract logo URL
                    logo_url = self.logo_extractor.extract_logo_url(website_url)
                    if logo_url:
                        enriched_data['logo_url'] = logo_url
                        logo_quality = self.logo_extractor.get_logo_quality_score(logo_url)
                        enriched_data['logo_quality_score'] = logo_quality
                        self.logger.info(f"Logo extracted for {tool_name}: {logo_url} (quality: {logo_quality:.2f})")
                    else:
                        enriched_data['logo_url'] = None
                        enriched_data['logo_quality_score'] = 0.0

                    # Discover additional URLs
                    discovered_urls = self.url_discoverer.discover_urls(website_url)
                    enriched_data.update(discovered_urls)

                    url_count = len([url for url in discovered_urls.values() if url])
                    self.logger.info(f"Discovered {url_count} additional URLs for {tool_name}")

                    self.logger.info(f"Successfully enriched data for {tool_name} with technical level: {technical_level} (confidence: {confidence:.2f})")
                    return enriched_data
                else:
                    self.logger.warning(f"Could not extract JSON from Perplexity response for {tool_name}")
                    return self._fallback_enrichment(tool_name, website_url, basic_description)
                    
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON response for {tool_name}: {str(e)}")
                return self._fallback_enrichment(tool_name, website_url, basic_description)
        
        return self._fallback_enrichment(tool_name, website_url, basic_description)
    
    def _fallback_enrichment(self, tool_name: str, website_url: str, basic_description: str) -> Dict[str, Any]:
        """Provide basic fallback data when API enrichment fails"""
        domain = urlparse(website_url).netloc.lower()
        
        # Basic categorization based on tool name patterns
        categories = []
        tags = []
        
        name_lower = tool_name.lower()
        if any(word in name_lower for word in ['chat', 'gpt', 'ai']):
            categories.append('AI Assistant')
            tags.extend(['AI', 'Assistant'])
        
        if any(word in name_lower for word in ['write', 'content', 'copy']):
            categories.append('Content Creation')
            tags.extend(['Writing', 'Content'])
            
        if any(word in name_lower for word in ['image', 'photo', 'visual']):
            categories.append('Image Generation')
            tags.extend(['Image', 'Visual'])
        
        fallback_data = {
            "short_description": basic_description or f"AI tool for enhanced productivity and automation",
            "description": basic_description or f"{tool_name} is an AI-powered tool designed to improve workflow efficiency and productivity.",
            "key_features": ["AI-powered", "User-friendly interface", "Productivity enhancement"],
            "use_cases": ["Business automation", "Personal productivity", "Creative tasks"],
            "pricing_model": "FREEMIUM",
            "price_range": "MEDIUM",
            "pricing_details": "Visit website for current pricing",
            "target_audience": ["Professionals", "Businesses"],
            "categories": categories if categories else ["AI Tools"],
            "tags": tags if tags else ["AI", "Productivity"],
            "has_free_tier": True,
            "api_access": False,
            "mobile_support": False,
            "integrations": [],
            "founded_year": None,
            "employee_count_range": "Unknown",
            "funding_stage": "Unknown"
        }

        # Add technical level classification even for fallback data
        technical_level = self.technical_classifier.classify_technical_level(fallback_data)
        fallback_data['technical_level'] = technical_level
        fallback_data['technical_level_confidence'] = 0.5  # Lower confidence for fallback

        # Try to extract logo even for fallback
        logo_url = self.logo_extractor.extract_logo_url(website_url)
        if logo_url:
            fallback_data['logo_url'] = logo_url
            fallback_data['logo_quality_score'] = self.logo_extractor.get_logo_quality_score(logo_url)
        else:
            fallback_data['logo_url'] = None
            fallback_data['logo_quality_score'] = 0.0

        # Try to discover URLs even for fallback
        discovered_urls = self.url_discoverer.discover_urls(website_url)
        fallback_data.update(discovered_urls)

        return fallback_data
    
    def get_company_info(self, tool_name: str, website_url: str) -> Dict[str, Any]:
        """Get company-specific information"""
        prompt = f"""
        Provide company information for "{tool_name}" (website: {website_url}) in JSON format:
        {{
            "founded_year": 2023,
            "employee_count_range": "1-10|11-50|51-200|201-500|501-1000|1001-5000|5000+",
            "funding_stage": "PRE_SEED|SEED|SERIES_A|SERIES_B|SERIES_C|SERIES_D_PLUS|PUBLIC|Unknown",
            "location_summary": "City, Country or Remote",
            "social_links": {{
                "twitter": "handle_without_@",
                "linkedin": "company/profile-name"
            }}
        }}
        
        Only include verified information. Use null for unknown fields.
        """
        
        response = self._call_perplexity(prompt, max_tokens=500)
        
        if response:
            try:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
        
        return {
            "founded_year": None,
            "employee_count_range": "Unknown", 
            "funding_stage": "Unknown",
            "location_summary": "Unknown",
            "social_links": {}
        }